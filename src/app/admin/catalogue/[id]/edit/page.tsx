'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { ArrowLeftIcon } from '@heroicons/react/24/outline';
import Link from 'next/link';

interface CatalogueParams {
  params: {
    id: string;
  };
}

export default function EditCataloguePage({ params }: CatalogueParams) {
  const router = useRouter();
  // Unwrap params Promise with React.use()
  const unwrappedParams = React.use(params);
  const id = unwrappedParams.id;
  const [loading, setLoading] = useState(false);
  const [loadingData, setLoadingData] = useState(true);
  const [formData, setFormData] = useState({
    service: '',
    price: '',
    description: '',
    icon: '',
    popular: false,
    features: [''], // Will be populated from database
    imageUrl: '',
    imageUrl2: '',
    imageUrl3: '',
    category: 'Other'
  });

  // For image upload
  const [imageFiles, setImageFiles] = useState<(File | null)[]>([null, null, null]);
  const [imagePreviews, setImagePreviews] = useState<string[]>(['', '', '']);
  const [uploadingImage, setUploadingImage] = useState(false);

  // For status messages
  const [statusMessage, setStatusMessage] = useState<{
    type: 'success' | 'error' | 'warning' | 'info' | null;
    message: string;
  }>({ type: null, message: '' });

  // Import the normalizeImageUrl function
  const normalizeImageUrl = (url: string): string => {
    if (!url) return '';

    // If it's already a relative URL, return as is
    if (url.startsWith('/')) {
      return url;
    }

    // Handle Linode Object Storage URLs without protocol
    if (url.includes('linodeobjects.com') && !url.startsWith('http')) {
      return `https://${url}`;
    }

    // Convert HTTP to HTTPS for Linode URLs
    if (url.startsWith('http://') && url.includes('linodeobjects.com')) {
      return url.replace('http://', 'https://');
    }

    // Handle URLs with double slashes (except after protocol)
    if (url.includes('//')) {
      // Fix double slashes in the path portion only
      const fixedUrl = url.replace(/([^:])\/\/+/g, '$1/');

      // If it's a Linode URL, also ensure it has a protocol
      if (fixedUrl.includes('linodeobjects.com') && !fixedUrl.startsWith('http')) {
        return `https://${fixedUrl}`;
      }

      return fixedUrl;
    }

    return url;
  };

  useEffect(() => {
    const fetchPricingItem = async () => {
      setLoadingData(true);
      try {
        const response = await fetch(`/api/admin/pricing/${id}`);
        if (response.ok) {
          const data = await response.json();

          // Normalize image URLs to ensure they have proper format
          const normalizedImageUrl = normalizeImageUrl(data.imageUrl || '');
          const normalizedImageUrl2 = normalizeImageUrl(data.imageUrl2 || '');
          const normalizedImageUrl3 = normalizeImageUrl(data.imageUrl3 || '');

          console.log('Original image URLs:', {
            imageUrl: data.imageUrl,
            imageUrl2: data.imageUrl2,
            imageUrl3: data.imageUrl3
          });

          console.log('Normalized image URLs:', {
            imageUrl: normalizedImageUrl,
            imageUrl2: normalizedImageUrl2,
            imageUrl3: normalizedImageUrl3
          });

          setFormData({
            service: data.service,
            price: data.price.toString(),
            description: data.description || '',
            icon: data.icon || '',
            popular: data.popular || false,
            features: Array.isArray(data.features) && data.features.length > 0
              ? data.features
              : [''], // At least one empty feature field
            imageUrl: normalizedImageUrl,
            imageUrl2: normalizedImageUrl2,
            imageUrl3: normalizedImageUrl3,
            category: data.category || 'Other'
          });

          // Set image previews for all available images
          const newImagePreviews = [
            normalizedImageUrl,
            normalizedImageUrl2,
            normalizedImageUrl3
          ];
          setImagePreviews(newImagePreviews);
        } else {
          alert('Failed to fetch pricing item');
          router.push('/admin/pricing');
        }
      } catch (error) {
        console.error('Error fetching pricing item:', error);
        alert('An error occurred while fetching the pricing item');
        router.push('/admin/pricing');
      } finally {
        setLoadingData(false);
      }
    };

    fetchPricingItem();
  }, [id, router]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target as HTMLInputElement; // Cast to access 'type' and 'checked'

    if (type === 'checkbox') {
      setFormData((prev) => ({ ...prev, [name]: (e.target as HTMLInputElement).checked }));
    } else if (name.startsWith('feature-')) {
      // Handle feature updates
      const index = parseInt(name.split('-')[1]);
      const newFeatures = [...formData.features];
      newFeatures[index] = value;
      setFormData((prev) => ({ ...prev, features: newFeatures }));
    } else if (type === 'file') {
      // Handle image file upload
      const files = (e.target as HTMLInputElement).files;
      if (files && files.length > 0) {
        const file = files[0];
        const imageIndex = parseInt(name.split('-')[1] || '0');

        // Update the specific image file in the array
        const newImageFiles = [...imageFiles];
        newImageFiles[imageIndex] = file;
        setImageFiles(newImageFiles);

        // Create a preview URL for this specific image
        const reader = new FileReader();
        reader.onloadend = () => {
          const newImagePreviews = [...imagePreviews];
          newImagePreviews[imageIndex] = reader.result as string;
          setImagePreviews(newImagePreviews);
        };
        reader.readAsDataURL(file);
      }
    } else {
      setFormData((prev) => ({ ...prev, [name]: value }));
    }
  };

  // Handle image upload to server
  const handleImageUpload = async (file: File, index: number): Promise<string | null> => {
    if (!file) return null;

    setUploadingImage(true);
    try {
      // Validate file size before uploading
      if (file.size > 5 * 1024 * 1024) { // 5MB
        console.error(`Image ${index + 1} is too large: ${Math.round(file.size / 1024 / 1024)}MB`);
        alert(`Image ${index + 1} is too large (${Math.round(file.size / 1024 / 1024)}MB). Maximum size is 5MB.`);
        return null;
      }

      // Validate file type
      const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
      if (!validTypes.includes(file.type)) {
        console.error(`Invalid file type for image ${index + 1}: ${file.type}`);
        alert(`Invalid file type for image ${index + 1}. Please upload a JPEG, PNG, GIF, or WebP image.`);
        return null;
      }

      console.log(`Uploading image ${index + 1}: ${file.name}, size: ${Math.round(file.size / 1024)}KB, type: ${file.type}`);

      const formData = new FormData();
      formData.append('file', file);
      formData.append('category', 'pricing');

      const response = await fetch('/api/upload', {
        method: 'POST',
        body: formData,
      });

      let data;
      try {
        data = await response.json();
      } catch (parseError) {
        console.error(`Error parsing response for image ${index + 1}:`, parseError);
        setStatusMessage({
          type: 'error',
          message: `Error processing server response for image ${index + 1}. The server may be experiencing issues.`
        });
        return null;
      }

      if (!response.ok) {
        const errorMessage = data.error || `Failed to upload image ${index + 1}`;
        console.error(`Upload error for image ${index + 1}:`, errorMessage);

        // Set status message instead of alert for better UX
        setStatusMessage({
          type: 'error',
          message: errorMessage
        });

        // If there's a fallback URL provided, use it for non-critical images
        if (data.fallbackUrl && (index > 0)) {
          console.warn(`Using fallback URL for optional image ${index + 1}`);
          return data.fallbackUrl;
        }

        return null;
      }

      // Check for warnings
      if (data.warning) {
        console.warn(`Upload warning for image ${index + 1}:`, data.warning);
        setStatusMessage({
          type: 'warning',
          message: data.warning
        });
      }

      // Show success message for important uploads
      if (index === 0 && data.success) {
        setStatusMessage({
          type: 'success',
          message: `Primary image uploaded successfully`
        });
      }

      console.log(`Successfully uploaded image ${index + 1}:`, data.url);
      return data.url;
    } catch (error) {
      console.error(`Error uploading image ${index + 1}:`, error);

      // Clean up any confusing error messages
      let errorMessage = `Failed to upload image ${index + 1}. Please try again.`;
      if (error instanceof Error) {
        const cleanedMessage = error.message
          .replace('AI image uploads failed', 'Image upload failed')
          .replace('Please check your commission privacy again', 'Please try again');
        errorMessage += ` Error: ${cleanedMessage}`;
      }

      setStatusMessage({
        type: 'error',
        message: errorMessage
      });

      // For non-primary images, we can continue with a null value
      if (index > 0) {
        return null;
      }

      // For primary image, show alert to ensure user sees it
      alert(errorMessage);
      return null;
    } finally {
      if (index === imageFiles.length - 1) {
        setUploadingImage(false);
      }
    }
  };

  // Upload all images and return their URLs
  const uploadAllImages = async (): Promise<string[]> => {
    setUploadingImage(true);
    const uploadPromises: Promise<string | null>[] = [];
    let hasErrors = false;

    try {
      // Count how many actual files we have to upload
      const filesToUpload = imageFiles.filter(file => file !== null);

      // If no files to upload, just return an empty array (we'll use existing URLs)
      if (filesToUpload.length === 0) {
        console.log('No new images to upload, using existing images');
        return [];
      }

      // Create upload promises for each image file
      imageFiles.forEach((file, index) => {
        if (file) {
          uploadPromises.push(handleImageUpload(file, index));
        } else {
          uploadPromises.push(Promise.resolve(null));
        }
      });

      // Wait for all uploads to complete
      const results = await Promise.all(uploadPromises);

      // Check if any uploads failed, but only for files that actually exist
      const attemptedUploads = filesToUpload.length;
      const failedUploads = results.filter((result, index) => imageFiles[index] !== null && result === null).length;

      if (failedUploads > 0 && attemptedUploads > failedUploads) {
        // Some uploads succeeded, some failed
        console.warn(`${failedUploads} out of ${attemptedUploads} image uploads failed`);
        alert(`Warning: ${failedUploads} out of ${attemptedUploads} image uploads failed. You can continue with the successfully uploaded images or try again.`);
        hasErrors = true;
      } else if (failedUploads > 0 && attemptedUploads === failedUploads && attemptedUploads > 0) {
        // All attempted uploads failed
        console.error('All image uploads failed');
        throw new Error('All image uploads failed. Please check your connection and try again.');
      }

      // Filter out null values and return the URLs
      return results.filter(Boolean) as string[];
    } catch (error) {
      console.error('Error uploading images:', error);
      alert(`Error uploading images: ${error instanceof Error ? error.message : 'Unknown error'}`);
      return [];
    } finally {
      setUploadingImage(false);
    }
  };

  // Add a new empty feature field
  const addFeatureField = () => {
    setFormData((prev) => ({
      ...prev,
      features: [...prev.features, '']
    }));
  };

  // Remove a feature field
  const removeFeatureField = (index: number) => {
    const newFeatures = [...formData.features];
    newFeatures.splice(index, 1);
    setFormData((prev) => ({
      ...prev,
      features: newFeatures.length > 0 ? newFeatures : [''] // Always keep at least one field
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      // Filter out empty features
      const filteredFeatures = formData.features.filter(feature => feature.trim() !== '');

      // Upload all images that were selected
      const uploadedImageUrls = await uploadAllImages();

      // Map the uploaded URLs to the corresponding imageUrl fields
      // If a new image was uploaded, use it; otherwise keep the existing URL
      const imageUrls = {
        imageUrl: uploadedImageUrls[0] || formData.imageUrl,
        imageUrl2: uploadedImageUrls[1] || formData.imageUrl2,
        imageUrl3: uploadedImageUrls[2] || formData.imageUrl3
      };

      const response = await fetch(`/api/admin/pricing/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          service: formData.service,
          price: parseInt(formData.price),
          description: formData.description,
          icon: formData.icon || null,
          popular: formData.popular,
          features: filteredFeatures,
          ...imageUrls,
          category: formData.category
        }),
      });

      if (response.ok) {
        // Show success message
        setStatusMessage({
          type: 'success',
          message: 'Pricing item updated successfully!'
        });

        // Short delay before redirect to allow user to see the success message
        setTimeout(() => {
          // Redirect to pricing list with refresh parameter
          router.push('/admin/pricing?refresh=' + new Date().getTime());
        }, 1500);
      } else {
        let errorData;
        try {
          errorData = await response.json();
        } catch (e) {
          errorData = { error: 'Failed to parse server response' };
        }

        // Clean up any confusing error messages
        const errorMessage = (errorData.error || 'Failed to update pricing item')
          .replace('AI image uploads failed', 'Image upload failed')
          .replace('Please check your commission privacy again', 'Please try again');

        setStatusMessage({
          type: 'error',
          message: `Error: ${errorMessage}`
        });

        // For critical errors, also show an alert
        if (response.status >= 500) {
          alert(`Server error: ${errorMessage}. Your changes may not have been saved.`);
        }
      }
    } catch (error) {
      console.error('Error updating pricing item:', error);

      // Clean up any confusing error messages
      let errorMessage = 'An error occurred while updating the pricing item';
      if (error instanceof Error) {
        const cleanedMessage = error.message
          .replace('AI image uploads failed', 'Image upload failed')
          .replace('Please check your commission privacy again', 'Please try again');
        errorMessage += `: ${cleanedMessage}`;
      }

      setStatusMessage({
        type: 'error',
        message: errorMessage
      });

      alert(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  if (loadingData) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-orange-500"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-2">
        <Link
          href="/admin/pricing"
          className="flex items-center text-sm text-slate-500 hover:text-slate-800"
        >
          <ArrowLeftIcon className="h-4 w-4 mr-1" />
          Back to Pricing
        </Link>
      </div>

      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-slate-800">Edit Pricing Item</h1>
      </div>

      {/* Status message display */}
      {statusMessage.type && (
        <div className={`p-4 rounded-md ${
          statusMessage.type === 'success' ? 'bg-green-50 text-green-800 border border-green-200' :
          statusMessage.type === 'error' ? 'bg-red-50 text-red-800 border border-red-200' :
          statusMessage.type === 'warning' ? 'bg-yellow-50 text-yellow-800 border border-yellow-200' :
          'bg-blue-50 text-blue-800 border border-blue-200'
        }`}>
          <div className="flex">
            <div className="flex-shrink-0">
              {statusMessage.type === 'success' && (
                <svg className="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
              )}
              {statusMessage.type === 'error' && (
                <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              )}
              {statusMessage.type === 'warning' && (
                <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
              )}
              {statusMessage.type === 'info' && (
                <svg className="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                </svg>
              )}
            </div>
            <div className="ml-3">
              <p className="text-sm">{statusMessage.message}</p>
            </div>
            <div className="ml-auto pl-3">
              <div className="-mx-1.5 -my-1.5">
                <button
                  type="button"
                  onClick={() => setStatusMessage({ type: null, message: '' })}
                  className={`inline-flex rounded-md p-1.5 ${
                    statusMessage.type === 'success' ? 'text-green-500 hover:bg-green-100' :
                    statusMessage.type === 'error' ? 'text-red-500 hover:bg-red-100' :
                    statusMessage.type === 'warning' ? 'text-yellow-500 hover:bg-yellow-100' :
                    'text-blue-500 hover:bg-blue-100'
                  } focus:outline-none`}
                >
                  <span className="sr-only">Dismiss</span>
                  <svg className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      <div className="bg-white rounded-lg shadow-sm p-6">
        <form onSubmit={handleSubmit} className="space-y-6">
          <div>
            <label htmlFor="service" className="block text-sm font-medium text-gray-700">
              Service Name
            </label>
            <input
              type="text"
              name="service"
              id="service"
              required
              value={formData.service}
              onChange={handleChange}
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-orange-500 focus:border-orange-500 sm:text-sm"
            />
          </div>

          <div>
            <label htmlFor="price" className="block text-sm font-medium text-gray-700">
              Price (KSh)
            </label>
            <input
              type="number"
              name="price"
              id="price"
              required
              value={formData.price}
              onChange={handleChange}
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-orange-500 focus:border-orange-500 sm:text-sm"
            />
          </div>

          <div>
            <label htmlFor="description" className="block text-sm font-medium text-gray-700">
              Description (Optional)
            </label>
            <textarea
              name="description"
              id="description"
              rows={3}
              value={formData.description}
              onChange={handleChange}
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-orange-500 focus:border-orange-500 sm:text-sm"
            />
          </div>

          <div>
            <label htmlFor="category" className="block text-sm font-medium text-gray-700">
              Category
            </label>
            <select
              id="category"
              name="category"
              value={formData.category}
              onChange={handleChange}
              className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-orange-500 focus:border-orange-500 sm:text-sm rounded-md"
            >
              <option value="Other">Other</option>
              <option value="Digital Services">Digital Services</option>
              <option value="Printing Services">Printing Services</option>
              <option value="Banners">Banners</option>
              <option value="Office Essentials">Office Essentials</option>
              <option value="Diaries & Notebooks">Diaries & Notebooks</option>
              <option value="Gift Sets">Gift Sets</option>
              <option value="Drinkware">Drinkware</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Product Images (Up to 3 images)
            </label>

            {/* Image Upload Section */}
            <div className="space-y-4">
              {/* Primary Image */}
              <div className="border border-gray-200 rounded-md p-4">
                <div className="flex items-center space-x-4">
                  {imagePreviews[0] ? (
                    <div className="relative">
                      <img
                        src={imagePreviews[0]}
                        alt="Primary image preview"
                        className="h-32 w-32 object-cover rounded-md"
                        onError={(e) => {
                          console.error('Image failed to load:', imagePreviews[0]);
                          // Try to fix the URL if it fails to load
                          const img = e.currentTarget;

                          // Try multiple fixes in sequence
                          if (img.src.includes('linodeobjects.com')) {
                            // Fix 1: Convert HTTP to HTTPS
                            if (img.src.startsWith('http://')) {
                              console.log('Attempting to fix image URL by converting HTTP to HTTPS');
                              img.src = img.src.replace('http://', 'https://');
                              return; // Try this fix first
                            }

                            // Fix 2: Add HTTPS protocol if missing
                            if (!img.src.startsWith('https://')) {
                              console.log('Attempting to fix image URL by adding HTTPS protocol');
                              img.src = `https://${img.src.replace(/^http:\/\//, '')}`;
                              return;
                            }

                            // Fix 3: Fix double slashes in path
                            if (img.src.includes('//')) {
                              const fixedUrl = img.src.replace(/([^:])\/\/+/g, '$1/');
                              if (fixedUrl !== img.src) {
                                console.log('Attempting to fix image URL by removing double slashes');
                                img.src = fixedUrl;
                                return;
                              }
                            }
                          }

                          // If all fixes fail, try a fallback image
                          console.log('All fixes failed, using fallback image');
                          img.src = '/images/placeholder.jpg';
                        }}
                      />
                      <button
                        type="button"
                        onClick={() => {
                          const newPreviews = [...imagePreviews];
                          newPreviews[0] = '';
                          setImagePreviews(newPreviews);

                          const newFiles = [...imageFiles];
                          newFiles[0] = null;
                          setImageFiles(newFiles);

                          setFormData(prev => ({ ...prev, imageUrl: '' }));
                        }}
                        className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600 focus:outline-none"
                        aria-label="Remove image"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                        </svg>
                      </button>
                    </div>
                  ) : (
                    <div className="h-32 w-32 border-2 border-dashed border-gray-300 rounded-md flex items-center justify-center text-gray-400">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                      </svg>
                    </div>
                  )}
                  <div className="flex-1">
                    <input
                      type="file"
                      id="image-0"
                      name="image-0"
                      accept="image/*"
                      onChange={handleChange}
                      className="sr-only"
                      ref={(input) => {
                        if (input && !imageFiles[0]) {
                          input.value = '';
                        }
                      }}
                    />
                    <label
                      htmlFor="image-0"
                      className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 cursor-pointer"
                    >
                      {imagePreviews[0] ? 'Change Primary Image' : 'Upload Primary Image'}
                    </label>
                    <p className="mt-1 text-xs text-gray-500">
                      This will be the main image shown for the product
                    </p>
                  </div>
                </div>
              </div>

              {/* Secondary Image */}
              <div className="border border-gray-200 rounded-md p-4">
                <div className="flex items-center space-x-4">
                  {imagePreviews[1] ? (
                    <div className="relative">
                      <img
                        src={imagePreviews[1]}
                        alt="Secondary image preview"
                        className="h-32 w-32 object-cover rounded-md"
                        onError={(e) => {
                          console.error('Image failed to load:', imagePreviews[1]);
                          // Try to fix the URL if it fails to load
                          const img = e.currentTarget;

                          // Try multiple fixes in sequence
                          if (img.src.includes('linodeobjects.com')) {
                            // Fix 1: Convert HTTP to HTTPS
                            if (img.src.startsWith('http://')) {
                              console.log('Attempting to fix image URL by converting HTTP to HTTPS');
                              img.src = img.src.replace('http://', 'https://');
                              return; // Try this fix first
                            }

                            // Fix 2: Add HTTPS protocol if missing
                            if (!img.src.startsWith('https://')) {
                              console.log('Attempting to fix image URL by adding HTTPS protocol');
                              img.src = `https://${img.src.replace(/^http:\/\//, '')}`;
                              return;
                            }

                            // Fix 3: Fix double slashes in path
                            if (img.src.includes('//')) {
                              const fixedUrl = img.src.replace(/([^:])\/\/+/g, '$1/');
                              if (fixedUrl !== img.src) {
                                console.log('Attempting to fix image URL by removing double slashes');
                                img.src = fixedUrl;
                                return;
                              }
                            }
                          }

                          // If all fixes fail, try a fallback image
                          console.log('All fixes failed, using fallback image');
                          img.src = '/images/placeholder.jpg';
                        }}
                      />
                      <button
                        type="button"
                        onClick={() => {
                          const newPreviews = [...imagePreviews];
                          newPreviews[1] = '';
                          setImagePreviews(newPreviews);

                          const newFiles = [...imageFiles];
                          newFiles[1] = null;
                          setImageFiles(newFiles);

                          setFormData(prev => ({ ...prev, imageUrl2: '' }));
                        }}
                        className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600 focus:outline-none"
                        aria-label="Remove image"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                        </svg>
                      </button>
                    </div>
                  ) : (
                    <div className="h-32 w-32 border-2 border-dashed border-gray-300 rounded-md flex items-center justify-center text-gray-400">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                      </svg>
                    </div>
                  )}
                  <div className="flex-1">
                    <input
                      type="file"
                      id="image-1"
                      name="image-1"
                      accept="image/*"
                      onChange={handleChange}
                      className="sr-only"
                      ref={(input) => {
                        if (input && !imageFiles[1]) {
                          input.value = '';
                        }
                      }}
                    />
                    <label
                      htmlFor="image-1"
                      className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 cursor-pointer"
                    >
                      {imagePreviews[1] ? 'Change Secondary Image' : 'Upload Secondary Image (Optional)'}
                    </label>
                    <p className="mt-1 text-xs text-gray-500">
                      Additional product view or detail
                    </p>
                  </div>
                </div>
              </div>

              {/* Tertiary Image */}
              <div className="border border-gray-200 rounded-md p-4">
                <div className="flex items-center space-x-4">
                  {imagePreviews[2] ? (
                    <div className="relative">
                      <img
                        src={imagePreviews[2]}
                        alt="Tertiary image preview"
                        className="h-32 w-32 object-cover rounded-md"
                        onError={(e) => {
                          console.error('Image failed to load:', imagePreviews[2]);
                          // Try to fix the URL if it fails to load
                          const img = e.currentTarget;

                          // Try multiple fixes in sequence
                          if (img.src.includes('linodeobjects.com')) {
                            // Fix 1: Convert HTTP to HTTPS
                            if (img.src.startsWith('http://')) {
                              console.log('Attempting to fix image URL by converting HTTP to HTTPS');
                              img.src = img.src.replace('http://', 'https://');
                              return; // Try this fix first
                            }

                            // Fix 2: Add HTTPS protocol if missing
                            if (!img.src.startsWith('https://')) {
                              console.log('Attempting to fix image URL by adding HTTPS protocol');
                              img.src = `https://${img.src.replace(/^http:\/\//, '')}`;
                              return;
                            }

                            // Fix 3: Fix double slashes in path
                            if (img.src.includes('//')) {
                              const fixedUrl = img.src.replace(/([^:])\/\/+/g, '$1/');
                              if (fixedUrl !== img.src) {
                                console.log('Attempting to fix image URL by removing double slashes');
                                img.src = fixedUrl;
                                return;
                              }
                            }
                          }

                          // If all fixes fail, try a fallback image
                          console.log('All fixes failed, using fallback image');
                          img.src = '/images/placeholder.jpg';
                        }}
                      />
                      <button
                        type="button"
                        onClick={() => {
                          const newPreviews = [...imagePreviews];
                          newPreviews[2] = '';
                          setImagePreviews(newPreviews);

                          const newFiles = [...imageFiles];
                          newFiles[2] = null;
                          setImageFiles(newFiles);

                          setFormData(prev => ({ ...prev, imageUrl3: '' }));
                        }}
                        className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600 focus:outline-none"
                        aria-label="Remove image"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                        </svg>
                      </button>
                    </div>
                  ) : (
                    <div className="h-32 w-32 border-2 border-dashed border-gray-300 rounded-md flex items-center justify-center text-gray-400">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                      </svg>
                    </div>
                  )}
                  <div className="flex-1">
                    <input
                      type="file"
                      id="image-2"
                      name="image-2"
                      accept="image/*"
                      onChange={handleChange}
                      className="sr-only"
                      ref={(input) => {
                        if (input && !imageFiles[2]) {
                          input.value = '';
                        }
                      }}
                    />
                    <label
                      htmlFor="image-2"
                      className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 cursor-pointer"
                    >
                      {imagePreviews[2] ? 'Change Tertiary Image' : 'Upload Tertiary Image (Optional)'}
                    </label>
                    <p className="mt-1 text-xs text-gray-500">
                      Additional product view or detail
                    </p>
                  </div>
                </div>
              </div>

              <p className="text-xs text-gray-500 mt-2">
                All images: JPG, PNG or GIF up to 5MB each. Images will be uploaded to S3 storage.
              </p>
            </div>
          </div>

          <div>
            <label htmlFor="icon" className="block text-sm font-medium text-gray-700">
              Icon (Optional)
            </label>
            <div className="mt-1 flex rounded-md shadow-sm">
              <span className="inline-flex items-center px-3 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-500 sm:text-sm">
                fa-
              </span>
              <input
                type="text"
                name="icon"
                id="icon"
                value={formData.icon}
                onChange={handleChange}
                className="flex-1 min-w-0 block w-full px-3 py-2 rounded-none rounded-r-md border border-gray-300 focus:ring-orange-500 focus:border-orange-500 sm:text-sm"
                placeholder="e.g. id-card, file, book-open"
              />
            </div>
            <p className="mt-1 text-sm text-gray-500">
              Enter a FontAwesome icon name without the "fa-" prefix. Used as fallback when no image is provided.
            </p>
          </div>

          <div className="flex items-center">
            <input
              id="popular"
              name="popular"
              type="checkbox"
              checked={formData.popular}
              onChange={handleChange}
              className="h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-300 rounded"
            />
            <label htmlFor="popular" className="ml-2 block text-sm text-gray-700">
              Mark as popular service
            </label>
          </div>

          <div>
            <div className="flex justify-between items-center mb-2">
              <label className="block text-sm font-medium text-gray-700">
                Service Features
              </label>
              <button
                type="button"
                onClick={addFeatureField}
                className="inline-flex items-center px-2.5 py-1.5 border border-transparent text-xs font-medium rounded text-orange-700 bg-orange-100 hover:bg-orange-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500"
              >
                Add Feature
              </button>
            </div>

            <div className="space-y-2">
              {formData.features.map((feature, index) => (
                <div key={index} className="flex items-center">
                  <input
                    type="text"
                    name={`feature-${index}`}
                    value={feature}
                    onChange={handleChange}
                    className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-orange-500 focus:border-orange-500 sm:text-sm"
                    placeholder={`Feature ${index + 1}`}
                  />
                  {formData.features.length > 1 && (
                    <button
                      type="button"
                      onClick={() => removeFeatureField(index)}
                      className="ml-2 inline-flex items-center p-1.5 border border-transparent rounded-full text-red-600 hover:bg-red-50 focus:outline-none"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </button>
                  )}
                </div>
              ))}
            </div>
            <p className="mt-1 text-sm text-gray-500">
              Add features that will be displayed on the pricing card. Leave empty to use default features.
            </p>
          </div>

          <div className="flex justify-end">
            <Link
              href="/admin/pricing"
              className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 mr-3"
            >
              Cancel
            </Link>
            <button
              type="submit"
              disabled={loading}
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-orange-500 hover:bg-orange-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500"
            >
              {loading ? 'Saving...' : 'Update Pricing Item'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}